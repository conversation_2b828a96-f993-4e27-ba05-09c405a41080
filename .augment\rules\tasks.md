---
type: "always_apply"
---

# Implementation Plan

- [ ] 1. Set up project foundation and core infrastructure
  - Create directory structure following MVC architecture
  - Set up Composer with required dependencies (GuzzleHttp, monolog/monolog, sentry/sentry, phpunit/phpunit)
  - Create single entry point public/index.php with basic routing
  - _Requirements: 5.1, 5.2, 5.3, 6.1, 6.6_

- [ ] 2. Implement core framework components
- [ ] 2.1 Create database connection and configuration system
  - Write Database.php class with PostgreSQL connection management
  - Create Config/Database.php for connection settings
  - Implement connection pooling and error handling
  - _Requirements: 5.4, 6.3_

- [ ] 2.2 Implement Redis connection and caching layer
  - Write Redis.php class for cache operations
  - Create connection management with fallback handling
  - Implement cache key patterns for installations and products
  - _Requirements: 2.6, 3.6_

- [ ] 2.3 Build central Router and ResponseFormatter
  - Create Router.php with pattern matching and route registration
  - Implement ResponseFormatter.php for consistent JSON API responses
  - Add request parsing and parameter validation
  - _Requirements: 5.3, 5.4_

- [ ] 3. Create database schema and setup system
- [ ] 3.1 Write complete SQL schema file
  - Create database/schema.sql with all tables (products, installations, ip_data, api_logs, admin_users)
  - Include all Freemius fields in products and installations tables
  - Add proper indexes for performance optimization
  - _Requirements: 1.5, 2.1, 6.2, 8.1_

- [ ] 3.2 Implement base model class and database operations
  - Create BaseModel.php with common CRUD operations
  - Implement query builder methods and prepared statements
  - Add transaction support and error handling
  - _Requirements: 5.6_

- [ ] 4. Build Freemius integration layer
- [ ] 4.1 Create Freemius data models
  - Implement Product.php model with all Freemius product fields
  - Create Installation.php model with complete installation data structure
  - Add methods for validation and webhook updates
  - _Requirements: 2.1, 2.2, 2.3, 2.4_

- [ ] 4.2 Implement FreemiusService for API integration
  - Create FreemiusService.php with installation validation logic
  - Implement caching layer for Freemius data with Redis
  - Add methods for product and installation verification
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5_

- [ ] 4.3 Build webhook processing system
  - Create WebhookController.php for Freemius webhook handling
  - Implement HMAC signature validation
  - Add Redis queue system for background processing
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7_

- [ ] 5. Implement IP intelligence system
- [ ] 5.1 Create IP data model and storage
  - Implement IpData.php model for complete ipRegistry data storage
  - Add methods for data freshness checking (3-day expiration)
  - Create efficient IP lookup with JSONB querying
  - _Requirements: 1.2, 1.3, 1.4, 1.5_

- [ ] 5.2 Build IpRegistryService integration
  - Create IpRegistryService.php with complete API integration
  - Implement data fetching that preserves entire ipRegistry response
  - Add caching logic and freshness validation
  - _Requirements: 1.2, 1.3, 1.4, 1.5_

- [ ] 6. Create main API endpoint
- [ ] 6.1 Implement ApiController for IP analysis
  - Create ApiController.php with /api/v1/analyze endpoint
  - Add request validation for all required parameters
  - Integrate Freemius validation and IP intelligence services
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6_

- [ ] 6.2 Add comprehensive API logging
  - Create ApiLog.php model for request/response logging
  - Implement LoggingService.php with structured logging
  - Add request tracking and performance monitoring
  - _Requirements: 1.6, 7.1, 7.2, 7.5, 7.6_

- [ ] 7. Build admin authentication system
- [ ] 7.1 Create user management and authentication
  - Implement User.php model with role-based access control
  - Create AuthService.php with email domain validation
  - Add session management and password security (12+ characters)
  - _Requirements: 4.2, 4.3, 8.1, 8.2, 8.6, 8.7_

- [ ] 7.2 Implement admin role system
  - Add role validation for Super Admin, Dev, Marketing, Sales
  - Create permission checking middleware
  - Implement role-based feature access control
  - _Requirements: 8.2, 8.3, 8.4, 8.5, 8.6, 8.7_

- [ ] 8. Create admin web interface
- [ ] 8.1 Build admin controller and routing
  - Create AdminController.php with authentication-protected routes
  - Implement dashboard with API usage statistics
  - Add admin navigation and layout system
  - _Requirements: 4.1, 4.4_

- [ ] 8.2 Implement dashboard and statistics views
  - Create Views/admin/dashboard.php with usage metrics
  - Add real-time statistics from api_logs table
  - Implement charts and data visualization
  - _Requirements: 4.4_

- [ ] 8.3 Build log viewing and search functionality
  - Create Views/admin/logs.php with search and filtering
  - Add pagination and export capabilities
  - Implement IP address and endpoint filtering
  - _Requirements: 4.5_

- [ ] 8.4 Add manual IP lookup and management tools
  - Create manual IP lookup interface with ipRegistry integration
  - Add IP data refresh and cache management tools
  - Implement Freemius data sync functionality
  - _Requirements: 4.6, 4.7_

- [ ] 9. Implement error handling and monitoring
- [ ] 9.1 Create comprehensive error handling system
  - Implement consistent error response formatting
  - Add error categorization (validation, authentication, service, rate limiting)
  - Create error logging with context and stack traces
  - _Requirements: 7.3, 7.4, 7.7_

- [ ] 9.2 Add monitoring and alerting integration
  - Integrate Sentry for error tracking and alerts
  - Implement performance monitoring and logging
  - Add health check endpoints for system monitoring
  - _Requirements: 7.7_

- [ ] 10. Create deployment configuration
- [ ] 10.1 Set up Apache configuration for shared hosting
  - Create public/.htaccess with URL rewriting rules
  - Add security headers and access controls
  - Configure PHP settings and error handling
  - _Requirements: 6.1, 6.4_

- [ ] 10.2 Build Docker development environment
  - Create docker-compose.yml with PHP, PostgreSQL, and Redis
  - Add development configuration and debugging tools
  - Include database initialization and sample data
  - _Requirements: 6.7_

- [ ] 11. Implement background job processing
- [ ] 11.1 Create webhook queue worker system
  - Build background worker for processing webhook events
  - Implement job retry logic and error handling
  - Add queue monitoring and management tools
  - _Requirements: 3.4, 3.5, 3.6_

- [ ] 11.2 Add scheduled maintenance tasks
  - Create cleanup jobs for old logs and expired cache
  - Implement IP data refresh scheduling
  - Add system health monitoring tasks
  - _Requirements: 7.6_

- [ ] 12. Final integration and testing setup
- [ ] 12.1 Create comprehensive testing environment
  - Set up test database with sample Freemius data
  - Create test IP addresses with known characteristics
  - Add admin users for each role type for testing
  - _Requirements: All requirements validation_

- [ ] 12.2 Build deployment documentation and scripts
  - Create README.md with complete setup instructions
  - Add database migration and configuration guides
  - Document API endpoints and admin interface usage
  - _Requirements: 6.2, 6.3, 6.4_

- [ ] 13. Performance optimization and security hardening
- [ ] 13.1 Optimize database queries and caching
  - Add database indexes for performance optimization
  - Implement query optimization and connection pooling
  - Fine-tune Redis caching strategies and TTL values
  - _Requirements: Performance and scalability_

- [ ] 13.2 Implement security best practices
  - Add input validation and SQL injection prevention
  - Implement rate limiting and DDoS protection
  - Add security headers and CSRF protection for admin interface
  - _Requirements: Security and data protection_