# GuardGeo API Platform - Implementation Memory

## Project Overview

The GuardGeo API Platform is a private proprietary backend system designed to serve the GuardGeo WordPress plugin with IP intelligence and fraud prevention capabilities. The platform validates plugin requests via Freemius integration, fetches IP intelligence data from ipRegistry, and provides comprehensive admin management tools.

**Core Architecture**: Pure PHP with MVC architecture following SOLID principles, deployable on shared hosting via FTP with a single SQL file for database setup.

## Critical Implementation Principles

### 1. Coherent Design Implementation
- **Task Coherence**: Each task must build upon previous tasks, maintaining architectural consistency
- **No Isolation**: Sub-tasks must integrate seamlessly with the overall system design
- **Progressive Development**: Follow the task sequence strictly to ensure proper dependency management
- **Cross-Component Integration**: Ensure all components (Controllers, Services, Models) work together cohesively

### 2. SOLID Principles Adherence
- **Single Responsibility**: Each class has one clear purpose
- **Open/Closed**: Classes open for extension, closed for modification
- **Liskov Substitution**: Derived classes must be substitutable for base classes
- **Interface Segregation**: Clients should not depend on interfaces they don't use
- **Dependency Inversion**: Depend on abstractions, not concretions

## Requirements

Sole authoritative documents for implementation:
- @specs\guardgeo-api-platform\requirements.md
- @specs\guardgeo-api-platform\design.md
- @specs\guardgeo-api-platform\tasks.md
- @ALL-FREEMIUS-ENTITIES.md